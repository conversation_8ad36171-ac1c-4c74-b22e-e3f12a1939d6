#!/usr/bin/env python3
"""
Evaluation Display Module - Handles position evaluation scoring display for the Chess GUI
"""

import tkinter as tk
from tkinter import ttk
import chess
from typing import Optional


class EvaluationDisplay:
    """
    Handles the evaluation scoring display for the chess GUI.
    Provides visual and textual representation of position evaluation.
    """
    
    def __init__(self, parent_frame: ttk.Frame, chess_bot):
        """
        Initialize the evaluation display.
        
        Args:
            parent_frame: The parent tkinter frame to contain the evaluation display
            chess_bot: The chess bot instance for evaluation calculations
        """
        self.parent_frame = parent_frame
        self.chess_bot = chess_bot
        self.current_score = 0
        
        self.setup_evaluation_ui()
    
    def setup_evaluation_ui(self):
        """Set up the evaluation display UI components."""
        # Evaluation group frame
        self.eval_group = ttk.LabelFrame(self.parent_frame, text="Position Evaluation", padding=10)
        self.eval_group.pack(fill=tk.X, pady=(0, 10))
        
        # Score display
        score_frame = ttk.Frame(self.eval_group)
        score_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(score_frame, text="Score:", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        self.score_label = ttk.Label(score_frame, text="0.00", font=("Arial", 12))
        self.score_label.pack(side=tk.LEFT, padx=(5, 0))
        
        # Evaluation bar
        bar_frame = ttk.Frame(self.eval_group)
        bar_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(bar_frame, text="Black", font=("Arial", 8)).pack(side=tk.LEFT)
        
        # Canvas for evaluation bar
        self.eval_canvas = tk.Canvas(bar_frame, height=20, bg="white", relief=tk.SUNKEN, bd=1)
        self.eval_canvas.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        ttk.Label(bar_frame, text="White", font=("Arial", 8)).pack(side=tk.RIGHT)
        
        # Position description
        self.description_label = ttk.Label(self.eval_group, text="Equal position", 
                                         font=("Arial", 10), foreground="blue")
        self.description_label.pack()
        
        # Additional evaluation details
        details_frame = ttk.Frame(self.eval_group)
        details_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.material_label = ttk.Label(details_frame, text="Material: 0", font=("Arial", 9))
        self.material_label.pack(anchor=tk.W)
        
        self.mobility_label = ttk.Label(details_frame, text="Mobility: 0", font=("Arial", 9))
        self.mobility_label.pack(anchor=tk.W)
    
    def calculate_detailed_evaluation(self, board: chess.Board) -> dict:
        """
        Calculate detailed evaluation breakdown.
        
        Args:
            board: Chess board to evaluate
            
        Returns:
            Dictionary containing evaluation details
        """
        if board.is_checkmate():
            total_score = -20000 if board.turn else 20000
            return {
                'total': total_score,
                'material': total_score,
                'mobility': 0,
                'positional': 0
            }
        
        if board.is_stalemate() or board.is_insufficient_material():
            return {
                'total': 0,
                'material': 0,
                'mobility': 0,
                'positional': 0
            }
        
        material_score = 0
        positional_score = 0
        
        # Material and positional evaluation
        for square in chess.SQUARES:
            piece = board.piece_at(square)
            if piece:
                base_value = self.chess_bot.piece_values[piece.piece_type]
                positional_bonus = 0
                
                # Add positional bonuses
                if piece.piece_type == chess.PAWN:
                    if piece.color == chess.WHITE:
                        positional_bonus = self.chess_bot.pawn_table[square]
                    else:
                        positional_bonus = self.chess_bot.pawn_table[chess.square_mirror(square)]
                elif piece.piece_type == chess.KNIGHT:
                    if piece.color == chess.WHITE:
                        positional_bonus = self.chess_bot.knight_table[square]
                    else:
                        positional_bonus = self.chess_bot.knight_table[chess.square_mirror(square)]
                
                if piece.color == chess.WHITE:
                    material_score += base_value
                    positional_score += positional_bonus
                else:
                    material_score -= base_value
                    positional_score -= positional_bonus
        
        # Mobility evaluation
        legal_moves = len(list(board.legal_moves))
        mobility_score = legal_moves * 2
        if board.turn == chess.BLACK:
            mobility_score = -mobility_score
        
        total_score = material_score + positional_score + mobility_score
        
        return {
            'total': total_score,
            'material': material_score,
            'mobility': mobility_score,
            'positional': positional_score
        }
    
    def format_score(self, score: int) -> str:
        """
        Format evaluation score for display.
        
        Args:
            score: Raw evaluation score
            
        Returns:
            Formatted score string
        """
        if abs(score) >= 10000:
            if score > 0:
                return "+M"  # Mate for White
            else:
                return "-M"  # Mate for Black
        
        # Convert to centipawn-like display (divide by 100)
        display_score = score / 100.0
        return f"{display_score:+.2f}"
    
    def get_position_description(self, score: int) -> tuple:
        """
        Get textual description and color for the position.
        
        Args:
            score: Evaluation score
            
        Returns:
            Tuple of (description_text, color)
        """
        if abs(score) >= 10000:
            if score > 0:
                return "White has mate!", "green"
            else:
                return "Black has mate!", "red"
        elif score > 300:
            return "White is winning", "green"
        elif score > 100:
            return "White is better", "darkgreen"
        elif score > 50:
            return "White is slightly better", "blue"
        elif score > -50:
            return "Equal position", "blue"
        elif score > -100:
            return "Black is slightly better", "blue"
        elif score > -300:
            return "Black is better", "darkred"
        else:
            return "Black is winning", "red"
    
    def draw_evaluation_bar(self, score: int):
        """
        Draw the evaluation bar showing position assessment.
        
        Args:
            score: Evaluation score to visualize
        """
        self.eval_canvas.delete("all")
        
        canvas_width = self.eval_canvas.winfo_width()
        canvas_height = self.eval_canvas.winfo_height()
        
        if canvas_width <= 1:  # Canvas not yet rendered
            self.eval_canvas.after(100, lambda: self.draw_evaluation_bar(score))
            return
        
        # Normalize score for display (clamp between -1000 and 1000)
        display_score = max(-1000, min(1000, score))
        
        # Calculate bar position (0 = center, positive = right, negative = left)
        center_x = canvas_width // 2
        bar_width = int((display_score / 1000.0) * (canvas_width // 2))
        
        # Draw background
        self.eval_canvas.create_rectangle(0, 0, canvas_width, canvas_height, 
                                        fill="lightgray", outline="gray")
        
        # Draw center line
        self.eval_canvas.create_line(center_x, 0, center_x, canvas_height, 
                                   fill="black", width=2)
        
        # Draw evaluation bar
        if bar_width > 0:  # White advantage
            self.eval_canvas.create_rectangle(center_x, 2, center_x + bar_width, canvas_height - 2,
                                            fill="lightgreen", outline="green")
        elif bar_width < 0:  # Black advantage
            self.eval_canvas.create_rectangle(center_x + bar_width, 2, center_x, canvas_height - 2,
                                            fill="lightcoral", outline="red")
    
    def update_evaluation(self, board: chess.Board):
        """
        Update the evaluation display with current position.
        
        Args:
            board: Current chess board position
        """
        try:
            # Calculate detailed evaluation
            eval_details = self.calculate_detailed_evaluation(board)
            self.current_score = eval_details['total']
            
            # Update score display
            formatted_score = self.format_score(self.current_score)
            self.score_label.config(text=formatted_score)
            
            # Update position description
            description, color = self.get_position_description(self.current_score)
            self.description_label.config(text=description, foreground=color)
            
            # Update detailed breakdown
            self.material_label.config(text=f"Material: {self.format_score(eval_details['material'])}")
            self.mobility_label.config(text=f"Mobility: {self.format_score(eval_details['mobility'])}")
            
            # Update evaluation bar
            self.draw_evaluation_bar(self.current_score)
            
        except Exception as e:
            # Fallback display on error
            self.score_label.config(text="Error")
            self.description_label.config(text="Evaluation error", foreground="red")
            print(f"Evaluation display error: {e}")
    
    def get_current_score(self) -> int:
        """
        Get the current evaluation score.
        
        Returns:
            Current evaluation score
        """
        return self.current_score

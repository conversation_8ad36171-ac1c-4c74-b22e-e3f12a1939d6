#!/usr/bin/env python3
"""
Chess Bot GUI - A desktop chess game with AI opponent using tkinter
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import chess
import threading
from chess_bot import ChessBot
from utils import save_game_pgn, analyze_position, format_analysis, get_opening_name
from evaluation_display import EvaluationDisplay

class ChessGUI:
    """
    GUI Chess game using tkinter with drag-and-drop functionality.
    """
    
    def __init__(self):
        """Initialize the chess GUI."""
        self.root = tk.Tk()
        self.root.title("Chess Bot - GUI Version")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        
        # Game state
        self.board = chess.Board()
        self.bot = ChessBot(depth=3, name="ChessBot")
        self.game_history = []
        self.human_color = chess.WHITE
        self.selected_square = None
        self.highlighted_squares = []
        self.game_over = False
        
        # Colors and styling
        self.light_square_color = "#F0D9B5"
        self.dark_square_color = "#B58863"
        self.highlight_color = "#FFFF00"
        self.selected_color = "#FF6B6B"
        self.legal_move_color = "#90EE90"
        
        # Unicode chess pieces
        self.piece_symbols = {
            'P': '♙', 'N': '♘', 'B': '♗', 'R': '♖', 'Q': '♕', 'K': '♔',  # White
            'p': '♟', 'n': '♞', 'b': '♝', 'r': '♜', 'q': '♛', 'k': '♚'   # Black
        }
        
        self.setup_ui()
        self.update_board_display()
        self.update_status()

        # Initialize evaluation display after UI setup
        self.evaluation_display.update_evaluation(self.board)
    
    def setup_ui(self):
        """Set up the user interface."""
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Left panel for the chess board
        board_frame = ttk.Frame(main_frame)
        board_frame.pack(side=tk.LEFT, padx=(0, 10))
        
        # Chess board canvas
        self.canvas = tk.Canvas(board_frame, width=480, height=480, bg="white")
        self.canvas.pack()
        self.canvas.bind("<Button-1>", self.on_square_click)
        
        # Right panel for controls and information
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # Game controls
        controls_group = ttk.LabelFrame(control_frame, text="Game Controls", padding=10)
        controls_group.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(controls_group, text="New Game", command=self.new_game).pack(fill=tk.X, pady=2)
        ttk.Button(controls_group, text="Switch Colors", command=self.switch_colors).pack(fill=tk.X, pady=2)
        ttk.Button(controls_group, text="Save Game", command=self.save_game).pack(fill=tk.X, pady=2)
        ttk.Button(controls_group, text="Load Game", command=self.load_game).pack(fill=tk.X, pady=2)
        
        # Bot difficulty
        difficulty_group = ttk.LabelFrame(control_frame, text="Bot Difficulty", padding=10)
        difficulty_group.pack(fill=tk.X, pady=(0, 10))
        
        self.difficulty_var = tk.StringVar(value="Medium")
        difficulties = [("Easy", 2), ("Medium", 3), ("Hard", 4), ("Expert", 5)]
        
        for name, depth in difficulties:
            ttk.Radiobutton(difficulty_group, text=f"{name} (depth {depth})", 
                           variable=self.difficulty_var, value=name,
                           command=lambda d=depth: self.set_difficulty(d)).pack(anchor=tk.W)
        
        # Game status
        status_group = ttk.LabelFrame(control_frame, text="Game Status", padding=10)
        status_group.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(status_group, text="White to move", font=("Arial", 12))
        self.status_label.pack()
        
        self.opening_label = ttk.Label(status_group, text="Opening: Starting Position", font=("Arial", 10))
        self.opening_label.pack()

        # Position evaluation display
        self.evaluation_display = EvaluationDisplay(control_frame, self.bot)

        # Move history
        history_group = ttk.LabelFrame(control_frame, text="Move History", padding=10)
        history_group.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Scrollable text widget for move history
        history_frame = ttk.Frame(history_group)
        history_frame.pack(fill=tk.BOTH, expand=True)
        
        self.history_text = tk.Text(history_frame, height=8, width=25, wrap=tk.WORD)
        history_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.history_text.yview)
        self.history_text.configure(yscrollcommand=history_scrollbar.set)
        
        self.history_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Analysis button
        ttk.Button(control_frame, text="Analyze Position", command=self.show_analysis).pack(fill=tk.X)
    
    def draw_board(self):
        """Draw the chess board."""
        self.canvas.delete("all")
        square_size = 60
        
        for row in range(8):
            for col in range(8):
                x1 = col * square_size
                y1 = row * square_size
                x2 = x1 + square_size
                y2 = y1 + square_size
                
                # Determine square color
                is_light = (row + col) % 2 == 0
                square = chess.square(col, 7 - row)
                
                # Choose color based on square state
                if square == self.selected_square:
                    color = self.selected_color
                elif square in self.highlighted_squares:
                    color = self.legal_move_color
                else:
                    color = self.light_square_color if is_light else self.dark_square_color
                
                # Draw square
                self.canvas.create_rectangle(x1, y1, x2, y2, fill=color, outline="black")
                
                # Add coordinates
                if col == 0:  # Rank labels
                    self.canvas.create_text(x1 + 5, y1 + 10, text=str(8 - row), 
                                          font=("Arial", 8), fill="black")
                if row == 7:  # File labels
                    self.canvas.create_text(x2 - 10, y2 - 5, text=chr(ord('a') + col), 
                                          font=("Arial", 8), fill="black")
    
    def draw_pieces(self):
        """Draw the chess pieces on the board."""
        square_size = 60
        
        for row in range(8):
            for col in range(8):
                square = chess.square(col, 7 - row)
                piece = self.board.piece_at(square)
                
                if piece:
                    x = col * square_size + square_size // 2
                    y = row * square_size + square_size // 2
                    
                    piece_symbol = self.piece_symbols.get(piece.symbol(), piece.symbol())
                    self.canvas.create_text(x, y, text=piece_symbol, 
                                          font=("Arial", 36), fill="black")
    
    def update_board_display(self):
        """Update the visual board display."""
        self.draw_board()
        self.draw_pieces()
    
    def on_square_click(self, event):
        """Handle mouse clicks on the board."""
        if self.game_over or self.board.turn != self.human_color:
            return
        
        square_size = 60
        col = event.x // square_size
        row = event.y // square_size
        
        if 0 <= col < 8 and 0 <= row < 8:
            clicked_square = chess.square(col, 7 - row)
            
            if self.selected_square is None:
                # Select a piece
                piece = self.board.piece_at(clicked_square)
                if piece and piece.color == self.human_color:
                    self.selected_square = clicked_square
                    self.highlight_legal_moves(clicked_square)
                    self.update_board_display()
            else:
                # Try to make a move
                move = chess.Move(self.selected_square, clicked_square)
                
                # Check for promotion
                piece = self.board.piece_at(self.selected_square)
                if (piece and piece.piece_type == chess.PAWN and 
                    ((piece.color == chess.WHITE and chess.square_rank(clicked_square) == 7) or
                     (piece.color == chess.BLACK and chess.square_rank(clicked_square) == 0))):
                    # Default to queen promotion for simplicity
                    move = chess.Move(self.selected_square, clicked_square, promotion=chess.QUEEN)
                
                if move in self.board.legal_moves:
                    self.make_move(move)
                
                # Clear selection
                self.selected_square = None
                self.highlighted_squares = []
                self.update_board_display()
    
    def highlight_legal_moves(self, square):
        """Highlight legal moves for the selected piece."""
        self.highlighted_squares = []
        for move in self.board.legal_moves:
            if move.from_square == square:
                self.highlighted_squares.append(move.to_square)
    
    def make_move(self, move):
        """Make a move on the board."""
        try:
            san_move = self.board.san(move)
            self.board.push(move)
            self.game_history.append(san_move)
            
            self.update_history_display()
            self.update_status()
            self.update_board_display()

            # Update evaluation display
            self.evaluation_display.update_evaluation(self.board)
            
            # Check for game over
            if self.board.is_game_over():
                self.handle_game_over()
            elif self.board.turn != self.human_color:
                # Bot's turn
                self.root.after(500, self.make_bot_move)  # Small delay for better UX
                
        except Exception as e:
            messagebox.showerror("Error", f"Invalid move: {e}")
    
    def make_bot_move(self):
        """Make a move for the bot in a separate thread."""
        def bot_move_thread():
            try:
                self.update_status("Bot is thinking...")
                move = self.bot.get_best_move(self.board)
                
                if move:
                    # Update UI in main thread
                    self.root.after(0, lambda: self.make_move(move))
                else:
                    self.root.after(0, lambda: messagebox.showinfo("Info", "Bot couldn't find a move!"))
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("Error", f"Bot error: {e}"))
        
        threading.Thread(target=bot_move_thread, daemon=True).start()
    
    def update_status(self, custom_message=None):
        """Update the game status display."""
        if custom_message:
            self.status_label.config(text=custom_message)
            return
        
        if self.board.is_checkmate():
            winner = "Black" if self.board.turn == chess.WHITE else "White"
            self.status_label.config(text=f"Checkmate! {winner} wins!")
            self.game_over = True
        elif self.board.is_stalemate():
            self.status_label.config(text="Stalemate! Draw!")
            self.game_over = True
        elif self.board.is_insufficient_material():
            self.status_label.config(text="Insufficient material! Draw!")
            self.game_over = True
        elif self.board.is_check():
            turn = "White" if self.board.turn == chess.WHITE else "Black"
            self.status_label.config(text=f"{turn} in check!")
        else:
            turn = "White" if self.board.turn == chess.WHITE else "Black"
            self.status_label.config(text=f"{turn} to move")
        
        # Update opening name
        if len(self.game_history) <= 8:
            opening = get_opening_name(self.board, self.game_history)
            self.opening_label.config(text=f"Opening: {opening}")
    
    def update_history_display(self):
        """Update the move history display."""
        self.history_text.delete(1.0, tk.END)
        
        # Format moves in pairs (White, Black)
        for i in range(0, len(self.game_history), 2):
            move_num = (i // 2) + 1
            white_move = self.game_history[i] if i < len(self.game_history) else ""
            black_move = self.game_history[i + 1] if i + 1 < len(self.game_history) else ""
            
            line = f"{move_num}. {white_move}"
            if black_move:
                line += f" {black_move}"
            line += "\n"
            
            self.history_text.insert(tk.END, line)
        
        # Scroll to bottom
        self.history_text.see(tk.END)
    
    def handle_game_over(self):
        """Handle game over situation."""
        self.game_over = True
        
        if self.board.is_checkmate():
            winner = "You" if self.board.turn != self.human_color else "ChessBot"
            message = f"Checkmate! {winner} win!"
        elif self.board.is_stalemate():
            message = "Stalemate! The game is a draw."
        elif self.board.is_insufficient_material():
            message = "Insufficient material! The game is a draw."
        else:
            message = "Game over!"
        
        result = messagebox.askyesno("Game Over", f"{message}\n\nWould you like to save this game?")
        if result:
            self.save_game()
    
    def new_game(self):
        """Start a new game."""
        self.board = chess.Board()
        self.game_history = []
        self.selected_square = None
        self.highlighted_squares = []
        self.game_over = False
        
        self.update_board_display()
        self.update_status()
        self.update_history_display()

        # Update evaluation display for new game
        self.evaluation_display.update_evaluation(self.board)
    
    def switch_colors(self):
        """Switch human and bot colors."""
        self.human_color = not self.human_color
        color_name = "White" if self.human_color == chess.WHITE else "Black"
        messagebox.showinfo("Colors Switched", f"You are now playing as {color_name}")
        
        # If it's now bot's turn, make a move
        if not self.game_over and self.board.turn != self.human_color:
            self.make_bot_move()
    
    def set_difficulty(self, depth):
        """Set the bot difficulty."""
        self.bot.set_depth(depth)
        difficulty_name = self.difficulty_var.get()
        messagebox.showinfo("Difficulty Changed", f"Bot difficulty set to {difficulty_name} (depth {depth})")
    
    def save_game(self):
        """Save the current game to a PGN file."""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".pgn",
                filetypes=[("PGN files", "*.pgn"), ("All files", "*.*")],
                title="Save Chess Game"
            )
            
            if filename:
                white_player = "Human" if self.human_color == chess.WHITE else "ChessBot"
                black_player = "ChessBot" if self.human_color == chess.WHITE else "Human"
                
                save_game_pgn(self.board, self.game_history, filename, white_player, black_player)
                messagebox.showinfo("Game Saved", f"Game saved to {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save game: {e}")
    
    def load_game(self):
        """Load a game from a PGN file."""
        try:
            filename = filedialog.askopenfilename(
                filetypes=[("PGN files", "*.pgn"), ("All files", "*.*")],
                title="Load Chess Game"
            )
            
            if filename:
                from utils import load_game_pgn
                board, history = load_game_pgn(filename)
                
                self.board = board
                self.game_history = history
                self.selected_square = None
                self.highlighted_squares = []
                self.game_over = self.board.is_game_over()
                
                self.update_board_display()
                self.update_status()
                self.update_history_display()

                # Update evaluation display for loaded game
                self.evaluation_display.update_evaluation(self.board)

                messagebox.showinfo("Game Loaded", f"Game loaded from {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load game: {e}")
    
    def show_analysis(self):
        """Show position analysis in a popup window."""
        try:
            analysis = analyze_position(self.board)
            analysis_text = format_analysis(analysis)
            
            # Create popup window
            popup = tk.Toplevel(self.root)
            popup.title("Position Analysis")
            popup.geometry("400x500")
            
            # Text widget with scrollbar
            frame = ttk.Frame(popup)
            frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            text_widget = tk.Text(frame, wrap=tk.WORD, font=("Courier", 10))
            scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)
            
            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            text_widget.insert(1.0, analysis_text)
            text_widget.config(state=tk.DISABLED)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to analyze position: {e}")
    
    def run(self):
        """Start the GUI application."""
        self.root.mainloop()

def main():
    """Main entry point for the GUI version."""
    try:
        app = ChessGUI()
        app.run()
    except Exception as e:
        print(f"Error starting GUI: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()